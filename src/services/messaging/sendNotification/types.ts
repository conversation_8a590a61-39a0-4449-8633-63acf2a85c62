import { Notification } from '../types/classes/Notification.js'
import { ServiceRequest } from '../../models/types/classes/ServiceRequest.js'
import * as Accounts from '../../accounts/types/index.js'

export interface SendFunctionArgs {
  notification: Notification
  user: Accounts.User | null | undefined
  resend: boolean
  sendFunctions: SendFunction[]
  sentMessagesCount: number
  index: number
  serviceRequest: ServiceRequest,
  resolve: () => void
  reject: (error: Error) => void
}

export type SendFunction = (args: SendFunctionArgs) => void
