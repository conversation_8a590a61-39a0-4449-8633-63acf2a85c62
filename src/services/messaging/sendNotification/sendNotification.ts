import { AppEventType } from '../../../app/types/AppEventType.js'
import { ErrorCode } from '../../../app/types/ErrorCode.js'
import { IModelsService } from '../../models/types/IModelsService.js'
import { ModelType } from '../../../app/types/ModelType.js'
import { Notification } from '../types/classes/Notification.js'
import { NotificationInfo } from '../types/NotificationInfo.js'
import { NotificationTemplate } from '../types/classes/NotificationTemplate.js'
import { ServiceName } from '../../../app/types/enums.js'
import * as Accounts from '../../accounts/types/index.js'
import * as Models from '../../models/types/index.js'
import appHelpers from '../../../app/helpers.js'
import getActiveSendFunctions from './helpers/getActiveSendFunctions.js'
import logger from '../../logger/index.js'
import saveSendReportToModel from './helpers/saveSendReportToModel.js'

const loadNotificationInfo = async (
  notificationId: string,
  serviceRequest: Models.ServiceRequest,
): Promise<NotificationInfo> => {
  const models = appHelpers.getCoreService<IModelsService>(ServiceName.models)

  const notification = await models.findObjectById<Notification>(
    ModelType.Notification,
    notificationId,
    undefined,
    serviceRequest,
  )
  if (!notification) {
    logger.error('services.messaging.sendNotification: notification not found.',
      { notificationId }, { remote: true })
    throw new Error(ErrorCode.notFound)
  }

  if (!notification.recipientId) {
    logger.error('services.messaging.sendNotification: notification.recipientId not set.',
      { notificationId }, { remote: true })
    throw new Error(ErrorCode.systemError)
  }

  const template = await models.findObjectById<NotificationTemplate>(
    ModelType.NotificationTemplate,
    notification.templateId,
    undefined,
    serviceRequest,
  )
  if (!template) {
    logger.error('services.messaging.sendNotification: notificationTemplate not found.',
      { notificationId }, { remote: true })
    throw new Error(ErrorCode.notFound)
  }

  const toUser = await models.findObjectById<Accounts.User>(
    ModelType.User,
    notification.recipientId,
    undefined,
    serviceRequest,
  )
  if (!toUser) {
    logger.error('services.messaging.sendNotification: toUser not found.',
      { notificationId }, { remote: true })
    throw new Error(ErrorCode.notFound)
  }

  return {
    notification,
    template,
    recipient: toUser,
  }
}

const sendNotification = (
  notificationId: string,
  resend: boolean,
  serviceRequest: Models.ServiceRequest,
): Promise<void> => {
  logger.info('services.messaging.sendNotification called.', { notificationId })

  return new Promise((resolve, reject) => {
    loadNotificationInfo(notificationId, serviceRequest)
      .then((notificationInfo) => {
        const {
          notification,
          template,
          recipient,
        } = notificationInfo
        if (!notification) {
          logger.error('services.messaging.sendNotification: message not found.',
            { notificationId }, { remote: true })
          resolve()
          return
        }

        if (!template) {
          logger.error('services.messaging.sendNotification: message template not given.',
            { notificationId }, { remote: true })
          resolve()
          return
        }

        if (
          !recipient &&
          (
            !notification.context ||
            (!notification.context.recipientEmail && !notification.context.recipientPhoneNumber)
          )
        ) {
          logger.error('services.messaging.sendNotification: recipient not specified.',
            { notificationId, notification }, { remote: true })
          resolve()
          return
        }

        if (
          !notification.sendEmail &&
          !notification.sendInAppMessage &&
          !notification.sendPushNotification &&
          !notification.sendSms
        ) {
          logger.info('services.messaging.sendNotification: the notification has no method enabled.',
            { notificationId, notification })
          resolve()
          return
        }

        const sendFunctions = getActiveSendFunctions({
          sendEmail: notification.sendEmail,
          sendInAppMessage: notification.sendInAppMessage,
          sendPushNotification: notification.sendPushNotification,
          sendSms: notification.sendSms,
        })

        if (!Array.isArray(sendFunctions) || sendFunctions.length < 1) {
          logger.error('services.messaging.sendNotification: no method enabled.',
            { notificationId, notification }, { remote: true })
          resolve()
          return
        }

        const done = () => {
          if (notification.multiStepActionId) {
            saveSendReportToModel(notification.id, serviceRequest)
          }

          appHelpers.publishAppEvent<Models.ObjectChangedAppEventData>(
            AppEventType.objectChanged,
            {
              objectId: notification.id,
              modelType: ModelType.Notification,
              messageType: Models.ObjectChangedEventType.updated,
              serviceRequest,
            },
          )

          logger.trace('services.messaging.sendNotification: done.',
            { notificationId, notification })
          resolve()
        }

        sendFunctions[0]({
          notification,
          user: recipient,
          resend,
          sendFunctions,
          sentMessagesCount: 0,
          index: 0,
          serviceRequest,
          resolve: done,
          reject,
        })
      }, (error: Error) => {
        logger.error('services.messaging.sendNotification: error.',
          { notificationId, error }, { remote: true })
      })
  })
}

export default sendNotification
