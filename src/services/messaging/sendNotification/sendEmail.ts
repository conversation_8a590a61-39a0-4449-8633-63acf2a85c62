import { IMessagingService } from '../../messaging/types/IMessagingService.js'
import { ModelType } from '../../../app/types/ModelType.js'
import { Notification } from '../types/classes/Notification.js'
import { NotificationContext } from '../types/classes/NotificationContext.js'
import { NotificationMethod } from '../types/enums.js'
import { SendFunctionArgs } from './types.js'
import { ServiceName } from '../../../app/types/enums.js'
import * as Models from '../../models/types/index.js'
import appHelpers from '../../../app/helpers.js'
import emailService from '../email/index.js'
import logger from '../../logger/index.js'
import next from './helpers/next.js'
import saveSendReportToNotification from './helpers/saveSendReportToNotification.js'

function send(
  args: SendFunctionArgs,
): void {
  const messaging = appHelpers.getCoreService<IMessagingService>(ServiceName.messaging)
  const {
    notification,
    user,
    index,
  } = args
  let email: string | null | undefined = notification.context &&
    notification.context.recipientEmail
  if (!email && user) {
    ({ email } = user)
  }

  if (!email) {
    logger.warn('services.messaging.sendNotification.sendEmail: user email not available.',
      { notification, index })
    next(args)
    return
  }

  const notificationTemplate = messaging.getTemplateById(
    notification.templateId,
  )

  if (!notificationTemplate) {
    logger.error('services.messaging.sendNotification.sendEmail: template not found.',
      { notification, index }, { remote: true })
    next(args)
    return
  }

  emailService.sendEmail(
    notification.id,
    args.serviceRequest,
    notification,
    notificationTemplate,
  ).then((result) => {
    const success = !result.error && !!result.messageId
    const sendReport = success
      ? `Message ID: ${result.messageId || 'N/A'}`
      : `Error: ${result.error || 'N/A'}`

    if (success) {
      args.sentMessagesCount += 1
    }

    saveSendReportToNotification(
      notification,
      NotificationMethod.email,
      sendReport,
      args.sentMessagesCount,
    ).then(() => {
      logger.trace('services.messaging.sendNotification.sendEmail done.',
        { notification, index, sendReport })
      next(args)
    }, (error: Error) => {
      logger.error('services.messaging.sendNotification.sendEmail: received error from saveSendReportToNotification.',
        { notification, index, error })
      next(args)
    })
  }, (error: Error) => {
    logger.error('services.messaging.sendNotification.sendEmail: received error.',
      { notification, index, error })
    saveSendReportToNotification(
      notification,
      NotificationMethod.email,
      `Error: ${error.message}`,
      args.sentMessagesCount,
    ).then(() => {
      next(args)
    }, (error: Error) => {
      logger.error('services.messaging.sendNotification.sendEmail: received error from saveSendReportToNotification.',
        { notification, index, error })
      next(args)
    })
  })
}

function sendEmail<ContextT extends NotificationContext>(
  args: SendFunctionArgs,
): void {
  logger.trace('services.messaging.sendNotification.sendEmail called.', { args })
  const models = appHelpers.getCoreService<Models.IModelsService>(ServiceName.models)
  const { notification, resend, index } = args

  if (!notification.sendEmail) {
    logger.trace('services.messaging.sendNotification.sendEmail: email not enabled.',
      { notification, index })
    next(args)
    return
  }

  if (!notification.templateId) {
    logger.error('services.messaging.sendNotification.sendEmail: no template ID given in notification.',
      { notification, index }, { remote: true })
    next(args)
    return
  }

  if (notification.emailSentAt) {
    if (!resend) {
      logger.trace('services.messaging.sendNotification.sendEmail: email was already sent.',
        { notification, index })
      next(args)
      return
    }
    models.upsertObject<Notification>(ModelType.Notification, {
      id: notification.id,
      emailSentAt: undefined,
      emailSendReport: '',
    },
    {},
    args.serviceRequest,
    ).then(() => {
      send(args)
    }, (error: Error) => {
      logger.error('services.messaging.sendNotification.sendEmail: failed to update notification.',
        { notification, index, error }, { remote: true })
    })
    return
  }

  send(args)
}

export default sendEmail
