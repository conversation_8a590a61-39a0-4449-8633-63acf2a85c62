import { NotificationTemplate } from './types/classes/NotificationTemplate.js'
import { NotificationTemplateName } from './types/enums.js'

const defaultNotificationTemplates: Partial<NotificationTemplate>[] = [
  {
    name: NotificationTemplateName.accountDeletedConfirmation,
    description: 'accountDeletedConfirmation',
    version: '0.1',
    titleAr: '',
    messageTextAr: '',
    shortMessageTextAr: '',
    htmlMessageAr: '',
    titleEn: '',
    messageTextEn: '',
    shortMessageTextEn: '',
    htmlMessageEn: '',
    titleEs: '',
    messageTextEs: '',
    shortMessageTextEs: '',
    htmlMessageEs: '',
    titleId: '',
    messageTextId: '',
    shortMessageTextId: '',
    htmlMessageId: '',
    titleRu: '',
    messageTextRu: '',
    shortMessageTextRu: '',
    htmlMessageRu: '',
    titleSo: '',
    messageTextSo: '',
    shortMessageTextSo: '',
    htmlMessageSo: '',
    sendEmail: true,
    // TODO: Add push notification when copy and translations are available
    sendPushNotification: false,
  },
  {
    name: NotificationTemplateName.channelInvitationAcceptedForMentee,
    description: 'Notifies the sender of an invitation that the recipient accepted it',
    version: '0.1',
    titleAr: 'قبل {{senderFullName}} دعوتك',
    messageTextAr: 'قبل {{senderFullName}} دعوتك للاتصال.',
    shortMessageTextAr: 'قبل {{senderFullName}} دعوتك للاتصال.',
    htmlMessageAr: '',
    titleEn: '{{senderFullName}} accepted your invitation',
    messageTextEn: `Hi {{recipientFirstName}},

{{senderFullName}} accepted your invitation to connect on First Spark. 

Open the conversation and ask {{senderFullName}} for advice.`,
    shortMessageTextEn: '{{senderFullName}} accepted your invitation to connect.',
    htmlMessageEn: '',
    titleEs: '{{senderFullName}} aceptó tu invitación',
    messageTextEs: '{{senderFullName}} aceptó su invitación para conectarse.',
    shortMessageTextEs: '{{senderFullName}} aceptó su invitación para conectarse.',
    htmlMessageEs: '',
    titleId: '{{senderFullName}} menerima undangan Anda.',
    messageTextId: '{{senderFullName}} menerima undangan Anda untuk terhubung.',
    shortMessageTextId: '{{senderFullName}} menerima undangan Anda untuk terhubung.',
    htmlMessageId: '',
    titleRu: '{{senderFullName}} принял(а) ваше приглашение',
    messageTextRu: '{{senderFullName}} принял(а) ваше приглашение присоединиться.',
    shortMessageTextRu: '{{senderFullName}} принял(а) ваше приглашение присоединиться.',
    htmlMessageRu: '',
    titleSo: '{{senderFullName}} wuu aqbalay martiqaadkaaga inaad ku xidho.',
    messageTextSo: '{{senderFullName}} wuu aqbalay martiqaadkaaga inaad ku xidho.',
    shortMessageTextSo: '{{senderFullName}} wuu aqbalay martiqaadkaaga inaad ku xidho.',
    htmlMessageSo: '',
    sendEmail: true,
    sendPushNotification: true,
  },
  {
    name: NotificationTemplateName.channelInvitationAcceptedForMentor,
    description: 'Notifies the sender of an invitation that the recipient accepted it',
    version: '0.1',
    titleAr: 'قبل {{senderFullName}} دعوتك',
    messageTextAr: 'قبل {{senderFullName}} دعوتك للاتصال.',
    shortMessageTextAr: 'قبل {{senderFullName}} دعوتك للاتصال.',
    htmlMessageAr: '',
    titleEn: '{{senderFullName}} accepted your invitation',
    messageTextEn: `Hi {{recipientFirstName}},

{{senderFullName}} accepted your invitation to connect on First Spark. 

Open the conversation and ask {{senderFullName}} for advice.`,
    shortMessageTextEn: '{{senderFullName}} accepted your invitation to connect.',
    htmlMessageEn: '',
    titleEs: '{{senderFullName}} aceptó tu invitación',
    messageTextEs: '{{senderFullName}} aceptó su invitación para conectarse.',
    shortMessageTextEs: '{{senderFullName}} aceptó su invitación para conectarse.',
    htmlMessageEs: '',
    titleId: '{{senderFullName}} menerima undangan Anda.',
    messageTextId: '{{senderFullName}} menerima undangan Anda untuk terhubung.',
    shortMessageTextId: '{{senderFullName}} menerima undangan Anda untuk terhubung.',
    htmlMessageId: '',
    titleRu: '{{senderFullName}} принял(а) ваше приглашение',
    messageTextRu: '{{senderFullName}} принял(а) ваше приглашение присоединиться.',
    shortMessageTextRu: '{{senderFullName}} принял(а) ваше приглашение присоединиться.',
    htmlMessageRu: '',
    titleSo: '{{senderFullName}} wuu aqbalay martiqaadkaaga inaad ku xidho.',
    messageTextSo: '{{senderFullName}} wuu aqbalay martiqaadkaaga inaad ku xidho.',
    shortMessageTextSo: '{{senderFullName}} wuu aqbalay martiqaadkaaga inaad ku xidho.',
    htmlMessageSo: '',
    sendEmail: true,
    sendPushNotification: true,
  },
  {
    name: NotificationTemplateName.channelInvitationDeclinedForMentee,
    description: 'Notifies the sender of an invitation that the recipient declined it',
    version: '0.1',
    titleAr: '',
    messageTextAr: '',
    shortMessageTextAr: '',
    htmlMessageAr: '',
    titleEn: '{{senderFullName}} declined your invitation',
    messageTextEn: `Hi {{recipientFirstName}},

{{senderFullName}} declined your invitation. They may be too busy or believe they can't help you. 

Don't let that discourage you. There are many more mentors you can connect with.`,
    shortMessageTextEn: '{{senderFullName}} declined your invitation to connect.',
    htmlMessageEn: '',
    titleEs: '',
    messageTextEs: '',
    shortMessageTextEs: '',
    htmlMessageEs: '',
    titleId: '',
    messageTextId: '',
    shortMessageTextId: '',
    htmlMessageId: '',
    titleRu: '',
    messageTextRu: '',
    shortMessageTextRu: '',
    htmlMessageRu: '',
    titleSo: '',
    messageTextSo: '',
    shortMessageTextSo: '',
    htmlMessageSo: '',
    sendEmail: false,
    sendPushNotification: false,
  },
  {
    name: NotificationTemplateName.channelInvitationDeclinedForMentor,
    description: 'Notifies the sender of an invitation that the recipient declined it',
    version: '0.1',
    titleAr: '',
    messageTextAr: '',
    shortMessageTextAr: '',
    htmlMessageAr: '',
    titleEn: '{{senderFullName}} declined your invitation',
    messageTextEn: `Hi {{recipientFirstName}},

{{senderFullName}} declined your invitation. They may already receive the help they were seeking. 

Don't let that discourage you. There are many more business owners you can connect with.`,
    shortMessageTextEn: '{{senderFullName}} declined your invitation to connect.',
    htmlMessageEn: '',
    titleEs: '',
    messageTextEs: '',
    shortMessageTextEs: '',
    htmlMessageEs: '',
    titleId: '',
    messageTextId: '',
    shortMessageTextId: '',
    htmlMessageId: '',
    titleRu: '',
    messageTextRu: '',
    shortMessageTextRu: '',
    htmlMessageRu: '',
    titleSo: '',
    messageTextSo: '',
    shortMessageTextSo: '',
    htmlMessageSo: '',
    sendEmail: false,
    sendPushNotification: false,
  },
  {
    name: NotificationTemplateName.channelInvitationReceivedForMentee,
    description: 'Notifies about a new channel invitation',
    version: '0.1',
    titleAr: 'دعوة اتصال جديدة',
    messageTextAr: 'يدعوك {{senderFullName}} للاتصال.',
    shortMessageTextAr: 'يدعوك {{senderFullName}} للاتصال.',
    htmlMessageAr: '',
    titleEn: 'New connect invitation',
    messageTextEn: '{{senderFullName}} invites you to connect.',
    shortMessageTextEn: '{{senderFullName}} invites you to connect.',
    htmlMessageEn: '',
    titleEs: 'Nueva invitación de conexión',
    messageTextEs: '{{senderFullName}} te invita a conectarte.',
    shortMessageTextEs: '{{senderFullName}} te invita a conectarte.',
    htmlMessageEs: '',
    titleId: '',
    messageTextId: '',
    shortMessageTextId: '',
    htmlMessageId: '',
    titleRu: '',
    messageTextRu: '',
    shortMessageTextRu: '',
    htmlMessageRu: '',
    titleSo: 'Martiqaad cusub oo isku xidha',
    messageTextSo: '{{senderFullName}} ayaa kugu martiqaadaya inaad ku xidhid.',
    shortMessageTextSo: '{{senderFullName}} ayaa kugu martiqaadaya inaad ku xidhid.',
    htmlMessageSo: '',
    sendEmail: true,
    sendPushNotification: true,
  },
  {
    name: NotificationTemplateName.channelInvitationReceivedForMentor,
    description: 'Notifies about a new channel invitation',
    version: '0.1',
    titleAr: 'دعوة اتصال جديدة',
    messageTextAr: 'يدعوك {{senderFullName}} للاتصال.',
    shortMessageTextAr: 'يدعوك {{senderFullName}} للاتصال.',
    htmlMessageAr: '',
    titleEn: 'New connect invitation',
    messageTextEn: '{{senderFullName}} invites you to connect.',
    shortMessageTextEn: '{{senderFullName}} invites you to connect.',
    htmlMessageEn: '',
    titleEs: 'Nueva invitación de conexión',
    messageTextEs: '{{senderFullName}} te invita a conectarte.',
    shortMessageTextEs: '{{senderFullName}} te invita a conectarte.',
    htmlMessageEs: '',
    titleId: '',
    messageTextId: '',
    shortMessageTextId: '',
    htmlMessageId: '',
    titleRu: '',
    messageTextRu: '',
    shortMessageTextRu: '',
    htmlMessageRu: '',
    titleSo: 'Martiqaad cusub oo isku xidha',
    messageTextSo: '{{senderFullName}} ayaa kugu martiqaadaya inaad ku xidhid.',
    shortMessageTextSo: '{{senderFullName}} ayaa kugu martiqaadaya inaad ku xidhid.',
    htmlMessageSo: '',
    sendEmail: true,
    sendPushNotification: true,
  },
  {
    name: NotificationTemplateName.channelMessageReceivedForMentee,
    description: 'Notifies about a new channel message',
    version: '0.1',
    titleAr: 'أرسل لك {{senderFullName}} رسالة',
    messageTextAr: '{{senderFullName}}: {{channelMessageText}}',
    shortMessageTextAr: '{{senderFullName}}: {{channelMessageText}}',
    htmlMessageAr: '',
    titleEn: '{{senderFullName}} sent you a message',
    messageTextEn: '{{senderFullName}}: {{channelMessageText}}',
    shortMessageTextEn: '{{senderFullName}} sent you a message',
    htmlMessageEn: '',
    titleEs: '{{senderFullName}} te envió un mensaje',
    messageTextEs: '{{senderFullName}}: {{channelMessageText}}',
    shortMessageTextEs: '{{senderFullName}}: {{channelMessageText}}',
    htmlMessageEs: '',
    titleId: '{{senderFullName}} mengirimkan pesan untukmu',
    messageTextId: '{{senderFullName}}: {{channelMessageText}}',
    shortMessageTextId: '{{senderFullName}}: {{channelMessageText}}',
    htmlMessageId: '',
    titleRu: '{{senderFullName}} отправил(а) вам сообщение',
    messageTextRu: '{{senderFullName}}: {{channelMessageText}}',
    shortMessageTextRu: '{{senderFullName}}: {{channelMessageText}}',
    htmlMessageRu: '',
    titleSo: '{{senderFullName}} ayaa kuu soo diray fariin',
    messageTextSo: '{{senderFullName}}: {{channelMessageText}}',
    shortMessageTextSo: '{{senderFullName}}: {{channelMessageText}}',
    htmlMessageSo: '',
    sendEmail: true,
    sendPushNotification: true,
  },
  {
    name: NotificationTemplateName.channelMessageReceivedForMentor,
    description: 'Notifies about a new channel message',
    version: '0.1',
    titleAr: 'أرسل لك {{senderFullName}} رسالة',
    messageTextAr: '{{senderFullName}}: {{channelMessageText}}',
    shortMessageTextAr: '{{senderFullName}}: {{channelMessageText}}',
    htmlMessageAr: '',
    titleEn: '{{senderFullName}} sent you a message',
    messageTextEn: '{{senderFullName}}: {{channelMessageText}}',
    shortMessageTextEn: '{{senderFullName}} sent you a message',
    htmlMessageEn: '',
    titleEs: '{{senderFullName}} te envió un mensaje',
    messageTextEs: '{{senderFullName}}: {{channelMessageText}}',
    shortMessageTextEs: '{{senderFullName}}: {{channelMessageText}}',
    htmlMessageEs: '',
    titleId: '{{senderFullName}} mengirimkan pesan untukmu',
    messageTextId: '{{senderFullName}}: {{channelMessageText}}',
    shortMessageTextId: '{{senderFullName}}: {{channelMessageText}}',
    htmlMessageId: '',
    titleRu: '{{senderFullName}} отправил(а) вам сообщение',
    messageTextRu: '{{senderFullName}}: {{channelMessageText}}',
    shortMessageTextRu: '{{senderFullName}}: {{channelMessageText}}',
    htmlMessageRu: '',
    titleSo: '{{senderFullName}} ayaa kuu soo diray fariin',
    messageTextSo: '{{senderFullName}}: {{channelMessageText}}',
    shortMessageTextSo: '{{senderFullName}}: {{channelMessageText}}',
    htmlMessageSo: '',
    sendEmail: true,
    sendPushNotification: true,
  },
  {
    name: NotificationTemplateName.completeProfileForMentee,
    description: 'completeProfileForMentee',
    version: '0.1',
    titleAr: '',
    messageTextAr: '',
    shortMessageTextAr: '',
    htmlMessageAr: '',
    titleEn: '',
    messageTextEn: '',
    shortMessageTextEn: '',
    htmlMessageEn: '',
    titleEs: '',
    messageTextEs: '',
    shortMessageTextEs: '',
    htmlMessageEs: '',
    titleId: '',
    messageTextId: '',
    shortMessageTextId: '',
    htmlMessageId: '',
    titleRu: '',
    messageTextRu: '',
    shortMessageTextRu: '',
    htmlMessageRu: '',
    titleSo: '',
    messageTextSo: '',
    shortMessageTextSo: '',
    htmlMessageSo: '',
    sendEmail: true,
    sendPushNotification: true,
  },
  {
    name: NotificationTemplateName.completeProfileForMentor,
    description: 'completeProfileForMentor',
    version: '0.1',
    titleAr: '',
    messageTextAr: '',
    shortMessageTextAr: '',
    htmlMessageAr: '',
    titleEn: '',
    messageTextEn: '',
    shortMessageTextEn: '',
    htmlMessageEn: '',
    titleEs: '',
    messageTextEs: '',
    shortMessageTextEs: '',
    htmlMessageEs: '',
    titleId: '',
    messageTextId: '',
    shortMessageTextId: '',
    htmlMessageId: '',
    titleRu: '',
    messageTextRu: '',
    shortMessageTextRu: '',
    htmlMessageRu: '',
    titleSo: '',
    messageTextSo: '',
    shortMessageTextSo: '',
    htmlMessageSo: '',
    sendEmail: true,
    sendPushNotification: true,
  },
  {
    name: NotificationTemplateName.completeSignUpForMentee,
    description: 'completeSignUpForMentee',
    version: '0.1',
    titleAr: '',
    messageTextAr: '',
    shortMessageTextAr: '',
    htmlMessageAr: '',
    titleEn: '',
    messageTextEn: '',
    shortMessageTextEn: '',
    htmlMessageEn: '',
    titleEs: '',
    messageTextEs: '',
    shortMessageTextEs: '',
    htmlMessageEs: '',
    titleId: '',
    messageTextId: '',
    shortMessageTextId: '',
    htmlMessageId: '',
    titleRu: '',
    messageTextRu: '',
    shortMessageTextRu: '',
    htmlMessageRu: '',
    titleSo: '',
    messageTextSo: '',
    shortMessageTextSo: '',
    htmlMessageSo: '',
    sendEmail: true,
    sendPushNotification: true,
  },
  {
    name: NotificationTemplateName.completeSignUpForMentor,
    description: 'completeSignUpForMentor',
    version: '0.1',
    titleAr: '',
    messageTextAr: '',
    shortMessageTextAr: '',
    htmlMessageAr: '',
    titleEn: '',
    messageTextEn: '',
    shortMessageTextEn: '',
    htmlMessageEn: '',
    titleEs: '',
    messageTextEs: '',
    shortMessageTextEs: '',
    htmlMessageEs: '',
    titleId: '',
    messageTextId: '',
    shortMessageTextId: '',
    htmlMessageId: '',
    titleRu: '',
    messageTextRu: '',
    shortMessageTextRu: '',
    htmlMessageRu: '',
    titleSo: '',
    messageTextSo: '',
    shortMessageTextSo: '',
    htmlMessageSo: '',
    sendEmail: true,
    sendPushNotification: true,
  },
  {
    name: NotificationTemplateName.matchesRecommendationsForMentee,
    description: 'Sent out periodically to mentees',
    version: '0.1',
    titleAr: '',
    messageTextAr: '',
    shortMessageTextAr: '',
    htmlMessageAr: '',
    titleEn: 'Mentors You May Be Interested In',
    messageTextEn: `Mentor Suggestions for You

We want to make it easy for you to find mentors to connect with. Click through, read more, 
and send a message if you're interested in making a connection.

f you're looking for inspiration, remember we've got plenty of tips in our Help Center .

Thanks for being part of our community!`,
    shortMessageTextEn: '{{senderFullName}} invites you to connect.',
    htmlMessageEn: '',
    titleEs: '',
    messageTextEs: '',
    shortMessageTextEs: '',
    htmlMessageEs: '',
    titleId: '',
    messageTextId: '',
    shortMessageTextId: '',
    htmlMessageId: '',
    titleRu: '',
    messageTextRu: '',
    shortMessageTextRu: '',
    htmlMessageRu: '',
    titleSo: '',
    messageTextSo: '',
    shortMessageTextSo: '',
    htmlMessageSo: '',
    sendEmail: true,
    sendPushNotification: true,
  },
  {
    name: NotificationTemplateName.matchesRecommendationsForMentor,
    description: 'Sent out periodically to mentors',
    version: '0.1',
    titleAr: '',
    messageTextAr: '',
    shortMessageTextAr: '',
    htmlMessageAr: '',
    titleEn: 'Mentees You May Be Interested In',
    messageTextEn: `Mentee Suggestions for You

We want to make it easy for you to find mentees to connect with. Click through, read more, 
and send a message if you're interested in making a connection.

f you're looking for inspiration, remember we've got plenty of tips in our Help Center .

Thanks for being part of our community!`,
    shortMessageTextEn: '{{senderFullName}} invites you to connect.',
    htmlMessageEn: '',
    titleEs: '',
    messageTextEs: '',
    shortMessageTextEs: '',
    htmlMessageEs: '',
    titleId: '',
    messageTextId: '',
    shortMessageTextId: '',
    htmlMessageId: '',
    titleRu: '',
    messageTextRu: '',
    shortMessageTextRu: '',
    htmlMessageRu: '',
    titleSo: '',
    messageTextSo: '',
    shortMessageTextSo: '',
    htmlMessageSo: '',
    sendEmail: true,
    sendPushNotification: true,
  },
  {
    name: NotificationTemplateName.newPrivacyRules,
    description: 'newPrivacyRules',
    version: '0.1',
    titleAr: '',
    messageTextAr: '',
    shortMessageTextAr: '',
    htmlMessageAr: '',
    titleEn: '',
    messageTextEn: '',
    shortMessageTextEn: '',
    htmlMessageEn: '',
    titleEs: '',
    messageTextEs: '',
    shortMessageTextEs: '',
    htmlMessageEs: '',
    titleId: '',
    messageTextId: '',
    shortMessageTextId: '',
    htmlMessageId: '',
    titleRu: '',
    messageTextRu: '',
    shortMessageTextRu: '',
    htmlMessageRu: '',
    titleSo: '',
    messageTextSo: '',
    shortMessageTextSo: '',
    htmlMessageSo: '',
    sendEmail: true,
    sendPushNotification: true,
  },
  {
    name: NotificationTemplateName.resetPasswordConfirmation,
    description: 'resetPasswordConfirmation',
    version: '0.1',
    titleAr: '',
    messageTextAr: '',
    shortMessageTextAr: '',
    htmlMessageAr: '',
    titleEn: '',
    messageTextEn: '',
    shortMessageTextEn: '',
    htmlMessageEn: '',
    titleEs: '',
    messageTextEs: '',
    shortMessageTextEs: '',
    htmlMessageEs: '',
    titleId: '',
    messageTextId: '',
    shortMessageTextId: '',
    htmlMessageId: '',
    titleRu: '',
    messageTextRu: '',
    shortMessageTextRu: '',
    htmlMessageRu: '',
    titleSo: '',
    messageTextSo: '',
    shortMessageTextSo: '',
    htmlMessageSo: '',
    sendEmail: true,
    // TODO: Add push notification when copy and translations are available
    sendPushNotification: false,
  },
  {
    name: NotificationTemplateName.resetPasswordConfirmToken,
    description: 'Reset password: confirm token',
    version: '0.1',
    titleAr: '',
    messageTextAr: '',
    shortMessageTextAr: '',
    htmlMessageAr: '',
    titleEn: 'First Spark code is {{confirmToken}}',
    messageTextEn: 'First Spark code is {{confirmToken}}',
    shortMessageTextEn: 'First Spark code is {{confirmToken}}',
    htmlMessageEn: '',
    titleEs: '',
    messageTextEs: '',
    shortMessageTextEs: '',
    htmlMessageEs: '',
    titleId: '',
    messageTextId: '',
    shortMessageTextId: '',
    htmlMessageId: '',
    titleRu: '',
    messageTextRu: '',
    shortMessageTextRu: '',
    htmlMessageRu: '',
    titleSo: '',
    messageTextSo: '',
    shortMessageTextSo: '',
    htmlMessageSo: '',
    sendEmail: true,
    sendPushNotification: false,
  },
  {
    name: NotificationTemplateName.sendFirstInvitationForMentee,
    description: 'sendFirstInvitationForMentee',
    version: '0.1',
    titleAr: '',
    messageTextAr: '',
    shortMessageTextAr: '',
    htmlMessageAr: '',
    titleEn: '',
    messageTextEn: '',
    shortMessageTextEn: '',
    htmlMessageEn: '',
    titleEs: '',
    messageTextEs: '',
    shortMessageTextEs: '',
    htmlMessageEs: '',
    titleId: '',
    messageTextId: '',
    shortMessageTextId: '',
    htmlMessageId: '',
    titleRu: '',
    messageTextRu: '',
    shortMessageTextRu: '',
    htmlMessageRu: '',
    titleSo: '',
    messageTextSo: '',
    shortMessageTextSo: '',
    htmlMessageSo: '',
    sendEmail: true,
    sendPushNotification: true,
  },
  {
    name: NotificationTemplateName.sendFirstInvitationForMentor,
    description: 'sendFirstInvitationForMentor',
    version: '0.1',
    titleAr: '',
    messageTextAr: '',
    shortMessageTextAr: '',
    htmlMessageAr: '',
    titleEn: '',
    messageTextEn: '',
    shortMessageTextEn: '',
    htmlMessageEn: '',
    titleEs: '',
    messageTextEs: '',
    shortMessageTextEs: '',
    htmlMessageEs: '',
    titleId: '',
    messageTextId: '',
    shortMessageTextId: '',
    htmlMessageId: '',
    titleRu: '',
    messageTextRu: '',
    shortMessageTextRu: '',
    htmlMessageRu: '',
    titleSo: '',
    messageTextSo: '',
    shortMessageTextSo: '',
    htmlMessageSo: '',
    sendEmail: true,
    sendPushNotification: true,
  },
  {
    name: NotificationTemplateName.welcomeForMentee,
    description: 'Welcome message to new mentees',
    version: '0.1',
    titleAr: '',
    messageTextAr: '',
    shortMessageTextAr: '',
    htmlMessageAr: '',
    titleEn: 'Welcome to First Spark',
    messageTextEn: 'Welcome to First Spark',
    shortMessageTextEn: '',
    htmlMessageEn: '',
    titleEs: '',
    messageTextEs: '',
    shortMessageTextEs: '',
    htmlMessageEs: '',
    titleId: '',
    messageTextId: '',
    shortMessageTextId: '',
    htmlMessageId: '',
    titleRu: '',
    messageTextRu: '',
    shortMessageTextRu: '',
    htmlMessageRu: '',
    titleSo: '',
    messageTextSo: '',
    shortMessageTextSo: '',
    htmlMessageSo: '',
    sendEmail: true,
    sendPushNotification: false,
  },
  {
    name: NotificationTemplateName.welcomeForMentor,
    description: 'Welcome message to new mentors',
    version: '0.1',
    titleAr: '',
    messageTextAr: '',
    shortMessageTextAr: '',
    htmlMessageAr: '',
    titleEn: 'Welcome to First Spark',
    messageTextEn: 'Welcome to First Spark',
    shortMessageTextEn: '',
    htmlMessageEn: '',
    titleEs: '',
    messageTextEs: '',
    shortMessageTextEs: '',
    htmlMessageEs: '',
    titleId: '',
    messageTextId: '',
    shortMessageTextId: '',
    htmlMessageId: '',
    titleRu: '',
    messageTextRu: '',
    shortMessageTextRu: '',
    htmlMessageRu: '',
    titleSo: '',
    messageTextSo: '',
    shortMessageTextSo: '',
    htmlMessageSo: '',
    sendEmail: true,
    sendPushNotification: false,
  },
]

export default defaultNotificationTemplates
