export enum NotificationMethod {
  off = 'off',
  auto = 'auto',
  email = 'email',
  sms = 'sms',
  pushNotification = 'pushNotification',
  inAppNotification = 'inAppNotification',
}

export enum NotificationType {
  accountDeletedConfirmation = 'accountDeletedConfirmation',
  channelInvitationAccepted = 'channelInvitationAccepted',
  channelInvitationDeclined = 'channelInvitationDeclined',
  channelInvitationReceived = 'channelInvitationReceived',
  channelMessageReceived = 'channelMessageReceived',
  completeProfile = 'completeProfile',
  completeSignUp = 'completeSignUp',
  matchesRecommendations = 'matchesRecommendations',
  newPrivacyRules = 'newPrivacyRules',
  newsletter = 'newsletter',
  resetPasswordConfirmation = 'resetPasswordConfirmation',
  resetPasswordConfirmToken = 'resetPasswordConfirmToken',
  sendFirstInvitation = 'sendFirstInvitation',
  unset = 'unset',
  welcome = 'welcome',
}

export enum NotificationTemplateName {
  accountDeletedConfirmation = 'accountDeletedConfirmation',
  channelInvitationAcceptedForMentee = 'channelInvitationAcceptedForMentee',
  channelInvitationAcceptedForMentor = 'channelInvitationAcceptedForMentor',
  channelInvitationDeclinedForMentee = 'channelInvitationDeclinedForMentee',
  channelInvitationDeclinedForMentor = 'channelInvitationDeclinedForMentor',
  channelInvitationReceivedForMentee = 'channelInvitationReceivedForMentee',
  channelInvitationReceivedForMentor = 'channelInvitationReceivedForMentor',
  channelMessageReceivedForMentee = 'channelMessageReceivedForMentee',
  channelMessageReceivedForMentor = 'channelMessageReceivedForMentor',
  completeProfileForMentee = 'completeProfileForMentee',
  completeProfileForMentor = 'completeProfileForMentor',
  completeSignUpForMentee = 'completeSignUpForMentee',
  completeSignUpForMentor = 'completeSignUpForMentor',
  matchesRecommendationsForMentee = 'matchesRecommendationsForMentee',
  matchesRecommendationsForMentor = 'matchesRecommendationsForMentor',
  newPrivacyRules = 'newPrivacyRules',
  newsletter = 'newsletter',
  resetPasswordConfirmation = 'resetPasswordConfirmation',
  resetPasswordConfirmToken = 'resetPasswordConfirmToken',
  sendFirstInvitationForMentee= 'sendFirstInvitationForMentee',
  sendFirstInvitationForMentor = 'sendFirstInvitationForMentor',
  unset = 'unset',
  welcomeForMentee = 'welcomeForMentee',
  welcomeForMentor = 'welcomeForMentor',
}

export enum SystemMessageStatus {
  planned = 'planned',
  active = 'active',
  paused = 'paused',
  finished = 'finished',
}

export enum SystemMessageType {
  welcome = 'welcome',
}

export enum TfaCommand {
  resetPassword = 'reset-password',
  updatePassword = 'update-password',
  verifyEmail = 'verify-email',
  verifyPhoneNumber = 'verify-phone-number',
  welcome = 'welcome',
}
