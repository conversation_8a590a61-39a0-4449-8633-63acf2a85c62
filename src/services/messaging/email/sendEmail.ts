import { ErrorCode } from '../../../app/types/ErrorCode.js'
import { IMessagingService } from '../types/IMessagingService.js'
import { IModelsService } from '../../models/types/IModelsService.js'
import { MessagingServiceStatus } from '../types/MessagingServiceStatus.js'
import { ModelType } from '../../../app/types/ModelType.js'
import { Notification } from '../types/classes/Notification.js'
import { NotificationTemplate } from '../types/classes/NotificationTemplate.js'
import { SendEmailResult } from '../types/SendEmailResult.js'
import { ServiceName } from '../../../app/types/enums.js'
import { ServiceRequest } from '../../models/types/classes/ServiceRequest.js'
import appHelpers from '../../../app/helpers.js'
import emailServiceData from './serviceData.js'
import logger from '../../logger/index.js'
import nodeMailer, { NodeMailerSendArgs } from './lib/nodemailer/index.js'
import renderEmail from './renderEmail.js'

let serviceStatus: MessagingServiceStatus | undefined

async function sendEmail(
  notificationId: string,
  serviceRequest: ServiceRequest,
  notification?: Notification | null,
  template?: NotificationTemplate | null,
): Promise<SendEmailResult> {
  logger.trace('messaging.email.sendEmail called.',
    { notificationId, notification, config: emailServiceData.getConfig() })

  const models = appHelpers.getCoreService<IModelsService>(ServiceName.models)

  if (!serviceStatus) {
    serviceStatus = appHelpers.getCoreService<IMessagingService>(ServiceName.messaging)
      .getServiceStatus() as MessagingServiceStatus
  }

  if (!notification) {
    notification = await models.findObjectById<Notification>(
      ModelType.Notification,
      notificationId,
      undefined,
      serviceRequest,
    )
    if (!notification) {
      logger.error('messaging.email.sendEmail: notification missing.',
        { notificationId })
      return { error: ErrorCode.notFound }
    }
  }

  if (!template) {
    template = await models.findObjectById<NotificationTemplate>(
      ModelType.NotificationTemplate,
      notification.templateId,
      undefined,
      serviceRequest,
    )
    if (!template) {
      logger.error('messaging.email.sendEmail: notification missing.',
        { notificationId })
      return { error: ErrorCode.notFound }
    }
  }

  const {
    subject,
    htmlBody,
    textBody,
    senderName,
    senderEmail,
    recipientFullName,
    recipientEmail,
  } = await renderEmail(
    notificationId,
    serviceRequest,
    notification,
    template,
  )

  const to = recipientFullName
    ? `${recipientFullName} <${recipientEmail}>`
    : recipientEmail

  // if (!transportType || transportType === EmailTransportType.SES) {
  const message: NodeMailerSendArgs = {
    from: `${senderName} <${senderEmail}>`,
    to,
    subject,
    htmlBody,
    textBody,
  }

  if (notification.context?.senderId && senderEmail) {
    message.replyTo = senderName
      ? `${senderName} <${senderEmail}>`
      : senderEmail
  }

  try {
    const response = await nodeMailer.send(message)
    logger.trace('messaging.email.sendEmail: nodeMailer responded.',
      { notification, response })

    // response is of type any
    if (response?.messageId) {
      if (serviceStatus) {
        serviceStatus.onSentEmail()
      }
      return { messageId: response.messageId }
    }

    return { error: 'no message ID in response' }
  } catch (error) {
    logger.error('messaging.email.sendEmail: nodeMailer threw error.',
      { notification, error }, { remote: true })
    if (serviceStatus) {
      serviceStatus.onFailedToSendEmail()
    }
    return { error: (error as Error).message }
  }
  // }

  // throw new Error(ErrorCode.notImplemented)
}

export default sendEmail
