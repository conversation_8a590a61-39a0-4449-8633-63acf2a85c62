import { NotificationContext } from '../../types/classes/NotificationContext.js'
import { NotificationContextAccountDeletedConfirmation } from '../../types/classes/NotificationContextAccountDeletedConfirmation.js'
import { NotificationContextAccountDeletedConfirmationInput } from '../../types/classes/NotificationContextAccountDeletedConfirmationInput.js'
import { NotificationContextChannelInvitationAcceptedForMentee } from '../../types/classes/NotificationContextChannelInvitationAcceptedForMentee.js'
import { NotificationContextChannelInvitationAcceptedForMenteeInput } from '../../types/classes/NotificationContextChannelInvitationAcceptedForMenteeInput.js'
import { NotificationContextChannelInvitationAcceptedForMentor } from '../../types/classes/NotificationContextChannelInvitationAcceptedForMentor.js'
import { NotificationContextChannelInvitationAcceptedForMentorInput } from '../../types/classes/NotificationContextChannelInvitationAcceptedForMentorInput.js'
import { NotificationContextChannelInvitationReceivedForMentee } from '../../types/classes/NotificationContextChannelInvitationReceivedForMentee.js'
import { NotificationContextChannelInvitationReceivedForMenteeInput } from '../../types/classes/NotificationContextChannelInvitationReceivedForMenteeInput.js'
import { NotificationContextChannelInvitationReceivedForMentor } from '../../types/classes/NotificationContextChannelInvitationReceivedForMentor.js'
import { NotificationContextChannelInvitationReceivedForMentorInput } from '../../types/classes/NotificationContextChannelInvitationReceivedForMentorInput.js'
import { NotificationContextChannelMessageReceivedForMentee } from '../../types/classes/NotificationContextChannelMessageReceivedForMentee.js'
import { NotificationContextChannelMessageReceivedForMenteeInput } from '../../types/classes/NotificationContextChannelMessageReceivedForMenteeInput.js'
import { NotificationContextChannelMessageReceivedForMentor } from '../../types/classes/NotificationContextChannelMessageReceivedForMentor.js'
import { NotificationContextChannelMessageReceivedForMentorInput } from '../../types/classes/NotificationContextChannelMessageReceivedForMentorInput.js'
import { NotificationContextCompleteProfileForMentee } from '../../types/classes/NotificationContextCompleteProfileForMentee.js'
import { NotificationContextCompleteProfileForMenteeInput } from '../../types/classes/NotificationContextCompleteProfileForMenteeInput.js'
import { NotificationContextCompleteProfileForMentor } from '../../types/classes/NotificationContextCompleteProfileForMentor.js'
import { NotificationContextCompleteProfileForMentorInput } from '../../types/classes/NotificationContextCompleteProfileForMentorInput.js'
import { NotificationContextCompleteSignUpForMentee } from '../../types/classes/NotificationContextCompleteSignUpForMentee.js'
import { NotificationContextCompleteSignUpForMenteeInput } from '../../types/classes/NotificationContextCompleteSignUpForMenteeInput.js'
import { NotificationContextCompleteSignUpForMentor } from '../../types/classes/NotificationContextCompleteSignUpForMentor.js'
import { NotificationContextCompleteSignUpForMentorInput } from '../../types/classes/NotificationContextCompleteSignUpForMentorInput.js'
import { NotificationContextInput } from '../../types/classes/NotificationContextInput.js'
import { NotificationContextMatchesRecommendationsForMentee } from '../../types/classes/NotificationContextMatchesRecommendationsForMentee.js'
import { NotificationContextMatchesRecommendationsForMenteeInput } from '../../types/classes/NotificationContextMatchesRecommendationsForMenteeInput.js'
import { NotificationContextMatchesRecommendationsForMentor } from '../../types/classes/NotificationContextMatchesRecommendationsForMentor.js'
import { NotificationContextMatchesRecommendationsForMentorInput } from '../../types/classes/NotificationContextMatchesRecommendationsForMentorInput.js'
import { NotificationContextNewPrivacyRules } from '../../types/classes/NotificationContextNewPrivacyRules.js'
import { NotificationContextNewPrivacyRulesInput } from '../../types/classes/NotificationContextNewPrivacyRulesInput.js'
import { NotificationContextResetPasswordConfirmation } from '../../types/classes/NotificationContextResetPasswordConfirmation.js'
import { NotificationContextResetPasswordConfirmationInput } from '../../types/classes/NotificationContextResetPasswordConfirmationInput.js'
import { NotificationContextResetPasswordConfirmToken } from '../../types/classes/NotificationContextResetPasswordConfirmToken.js'
import { NotificationContextResetPasswordConfirmTokenInput } from '../../types/classes/NotificationContextResetPasswordConfirmTokenInput.js'
import { NotificationContextSendFirstInvitationForMentee } from '../../types/classes/NotificationContextSendFirstInvitationForMentee.js'
import { NotificationContextSendFirstInvitationForMenteeInput } from '../../types/classes/NotificationContextSendFirstInvitationForMenteeInput.js'
import { NotificationContextSendFirstInvitationForMentor } from '../../types/classes/NotificationContextSendFirstInvitationForMentor.js'
import { NotificationContextSendFirstInvitationForMentorInput } from '../../types/classes/NotificationContextSendFirstInvitationForMentorInput.js'
import { NotificationContextWelcomeForMentee } from '../../types/classes/NotificationContextWelcomeForMentee.js'
import { NotificationContextWelcomeForMenteeInput } from '../../types/classes/NotificationContextWelcomeForMenteeInput.js'
import { NotificationContextWelcomeForMentor } from '../../types/classes/NotificationContextWelcomeForMentor.js'
import { NotificationContextWelcomeForMentorInput } from '../../types/classes/NotificationContextWelcomeForMentorInput.js'
import { NotificationType } from '../../types/enums.js'
import * as Accounts from '../../../accounts/types/index.js'

const createContext = (
  notificationType: NotificationType,
  useInputType: boolean,
  attributes?: Partial<NotificationContext>,
  sender?: Accounts.User | null,
  recipient?: Accounts.User | null,
): NotificationContext | NotificationContextInput => {
  const forMentee = !(recipient && recipient.offersHelp) // defaulting to a mentee

  switch (notificationType) {
  case NotificationType.accountDeletedConfirmation: {
    if (useInputType) {
      return new NotificationContextAccountDeletedConfirmationInput(attributes, sender, recipient)
    }
    return new NotificationContextAccountDeletedConfirmation(attributes, sender, recipient)
  }

  case NotificationType.channelInvitationAccepted: {
    if (forMentee) {
      if (useInputType) {
        return new NotificationContextChannelInvitationAcceptedForMenteeInput(attributes, sender, recipient)
      }
      return new NotificationContextChannelInvitationAcceptedForMentee(attributes, sender, recipient)
    }
    if (useInputType) {
      return new NotificationContextChannelInvitationAcceptedForMentorInput(attributes, sender, recipient)
    }
    return new NotificationContextChannelInvitationAcceptedForMentor(attributes, sender, recipient)
  }

  case NotificationType.channelInvitationDeclined: {
    // if (forMentee) {
    //   if (useInputType) {
    //     return new NotificationContextChannelInvitationDeclinedForMenteeInput(attributes, sender, recipient)
    //   }
    //   return new NotificationContextChannelInvitationDeclinedForMentee(attributes, sender, recipient)
    // }
    // if (useInputType) {
    //   return new NotificationContextChannelInvitationDeclinedForMentorInput(attributes, sender, recipient)
    // }
    // return new NotificationContextChannelInvitationDeclinedForMentor(attributes, sender, recipient)
    if (useInputType) {
      return new NotificationContextInput(attributes, sender, recipient)
    }
    return new NotificationContext(attributes, sender, recipient)
  }

  case NotificationType.channelInvitationReceived: {
    if (forMentee) {
      if (useInputType) {
        return new NotificationContextChannelInvitationReceivedForMenteeInput(attributes, sender, recipient)
      }
      return new NotificationContextChannelInvitationReceivedForMentee(attributes, sender, recipient)
    }
    if (useInputType) {
      return new NotificationContextChannelInvitationReceivedForMentorInput(attributes, sender, recipient)
    }
    return new NotificationContextChannelInvitationReceivedForMentor(attributes, sender, recipient)
  }

  case NotificationType.channelMessageReceived: {
    if (forMentee) {
      if (useInputType) {
        return new NotificationContextChannelMessageReceivedForMenteeInput(attributes, sender, recipient)
      }
      return new NotificationContextChannelMessageReceivedForMentee(attributes, sender, recipient)
    }
    if (useInputType) {
      return new NotificationContextChannelMessageReceivedForMentorInput(attributes, sender, recipient)
    }
    return new NotificationContextChannelMessageReceivedForMentor(attributes, sender, recipient)
  }

  case NotificationType.completeProfile: {
    if (forMentee) {
      if (useInputType) {
        return new NotificationContextCompleteProfileForMenteeInput(attributes, sender, recipient)
      }
      return new NotificationContextCompleteProfileForMentee(attributes, sender, recipient)
    }
    if (useInputType) {
      return new NotificationContextCompleteProfileForMentorInput(attributes, sender, recipient)
    }
    return new NotificationContextCompleteProfileForMentor(attributes, sender, recipient)
  }

  case NotificationType.completeSignUp: {
    if (forMentee) {
      if (useInputType) {
        return new NotificationContextCompleteSignUpForMenteeInput(attributes, sender, recipient)
      }
      return new NotificationContextCompleteSignUpForMentee(attributes, sender, recipient)
    }
    if (useInputType) {
      return new NotificationContextCompleteSignUpForMentorInput(attributes, sender, recipient)
    }
    return new NotificationContextCompleteSignUpForMentor(attributes, sender, recipient)
  }

  case NotificationType.matchesRecommendations:{
    if (forMentee) {
      if (useInputType) {
        return new NotificationContextMatchesRecommendationsForMenteeInput(attributes, sender, recipient)
      }
      return new NotificationContextMatchesRecommendationsForMentee(attributes, sender, recipient)
    }
    if (useInputType) {
      return new NotificationContextMatchesRecommendationsForMentorInput(attributes, sender, recipient)
    }
    return new NotificationContextMatchesRecommendationsForMentor(attributes, sender, recipient)
  }

  case NotificationType.newPrivacyRules:
    if (useInputType) {
      return new NotificationContextNewPrivacyRulesInput(attributes, sender, recipient)
    }
    return new NotificationContextNewPrivacyRules(attributes, sender, recipient)

  case NotificationType.newsletter:
    if (useInputType) {
      return new NotificationContext(attributes, sender, recipient)
    }
    return new NotificationContext(attributes, sender, recipient)

  case NotificationType.resetPasswordConfirmation:
    if (useInputType) {
      return new NotificationContextResetPasswordConfirmationInput(attributes, sender, recipient)
    }
    return new NotificationContextResetPasswordConfirmation(attributes, sender, recipient)

  case NotificationType.resetPasswordConfirmToken:
    if (useInputType) {
      return new NotificationContextResetPasswordConfirmTokenInput(attributes, sender, recipient)
    }
    return new NotificationContextResetPasswordConfirmToken(attributes, sender, recipient)

  case NotificationType.sendFirstInvitation: {
    if (forMentee) {
      if (useInputType) {
        return new NotificationContextSendFirstInvitationForMenteeInput(attributes, sender, recipient)
      }
      return new NotificationContextSendFirstInvitationForMentee(attributes, sender, recipient)
    }
    if (useInputType) {
      return new NotificationContextSendFirstInvitationForMentorInput(attributes, sender, recipient)
    }
    return new NotificationContextSendFirstInvitationForMentor(attributes, sender, recipient)
  }

  case NotificationType.unset:
    if (useInputType) {
      return new NotificationContextInput(attributes, sender, recipient)
    }
    return new NotificationContext(attributes, sender, recipient)

  case NotificationType.welcome: {
    if (forMentee) {
      if (useInputType) {
        return new NotificationContextWelcomeForMenteeInput(attributes, sender, recipient)
      }
      return new NotificationContextWelcomeForMentee(attributes, sender, recipient)
    }
    if (useInputType) {
      return new NotificationContextWelcomeForMentorInput(attributes, sender, recipient)
    }
    return new NotificationContextWelcomeForMentor(attributes, sender, recipient)
  }
  }
}

export default createContext
