import { AppEventType } from '../../app/types/AppEventType.js'
import { ErrorCode } from '../../app/types/ErrorCode.js'
import { ModelType } from '../../app/types/ModelType.js'
import { Notification } from './types/classes/Notification.js'
import { NotificationInput } from './types/classes/NotificationInput.js'
import { NotificationTemplate } from './types/classes/NotificationTemplate.js'
import { NotificationTemplateName } from './types/enums.js'
import { ServiceName } from '../../app/types/enums.js'
import { User } from '../accounts/types/classes/User.js'
import * as Models from '../models/types/index.js'
import * as SecureId from '../secureId/types/index.js'
import activateNotificationMethods from './serviceHelpers/activateNotificationMethods.js'
import appHelpers from '../../app/helpers.js'
import applyTemplateFunc from './serviceHelpers/applyTemplate.js'
import getNotificationObjectFromInput from './getNotificationObjectFromInput.js'
import getTemplateById from './getTemplateById.js'
import getTemplateByName from './getTemplateByName.js'
import getTemplateNameByNotificationType from './getTemplateNameByNotificationType.js'
import logger from '../logger/index.js'
import sendNotification from './sendNotification/sendNotification.js'

const createNotification = async (
  notificationInput: NotificationInput,
  recipient: User | null | undefined,
  applyTemplate: boolean,
  sendOut: boolean,
  serviceRequest: Models.ServiceRequest,
): Promise<Notification> => {
  logger.trace('services.messaging.createNotification called.',
    { recipientId: recipient?.id, notificationInput })
  const models = appHelpers.getCoreService<Models.IModelsService>(ServiceName.models)
  const secureId = appHelpers.getCoreService<SecureId.ISecureIdService>(ServiceName.secureId)
  let sender: User | null | undefined
  let template: NotificationTemplate | null | undefined

  if (!notificationInput.notificationType) {
    logger.error('services.messaging.createNotification: notificationType not specified.',
      { notificationInput }, { remote: true })
    throw new Error(ErrorCode.invalidInput)
  }

  if (notificationInput.context?.senderId) {
    sender = await models.findObjectById<User>(
      ModelType.User,
      notificationInput.context?.senderId,
      undefined,
      serviceRequest,
    )
  }

  if (recipient) {
    notificationInput.recipientId = recipient.id
  } else if (notificationInput.recipientId) {
    recipient = await models.findObjectById<User>(
      ModelType.User,
      notificationInput.recipientId,
      undefined,
      serviceRequest,
    )
  }

  if (!recipient) {
    logger.error('services.messaging.createNotification: recipient not found.',
      { notificationInput, serviceRequest }, { remote: true })
    throw new Error(ErrorCode.notFound)
  }

  if (!notificationInput.language) {
    notificationInput.language = recipient.selectedUiLanguageTextId ??
      recipient.fallbackUiLanguageTextId
  }

  if (recipient?.suspendedAt && !notificationInput.allowSendingToSuspendedUser) {
    // We don't want to engage suspended users, unless expressively allowed
    logger.warn('services.messaging.createNotification: recipient is suspended.',
      { notificationInput, serviceRequest })
  }

  if (!notificationInput.templateId && !notificationInput.templateName) {
    notificationInput.templateName = getTemplateNameByNotificationType(
      notificationInput.notificationType,
      sender,
      recipient,
    )
  }

  if (applyTemplate) {
    template = notificationInput.templateId
      ? getTemplateById(notificationInput.templateId)
      : getTemplateByName(notificationInput.templateName as NotificationTemplateName | string)

    if (!template) {
      logger.error('services.messaging.createNotification: failed to load template.',
        { notificationInput }, { remote: true })
      throw new Error(ErrorCode.notFound)
    }

    notificationInput = applyTemplateFunc(
      notificationInput,
      template,
    )
  }

  if (!notificationInput.notificationType) {
    logger.error('services.messaging.createNotification: notificationType not specified.',
      { notificationInput }, { remote: true })
    throw new Error(ErrorCode.invalidInput)
  }

  const options = await secureId.models.user.getPreferencesForNotificationType(
    recipient.id,
    notificationInput.notificationType,
    serviceRequest,
    recipient,
  )
  notificationInput = activateNotificationMethods(
    notificationInput,
    options,
    template,
  )

  const notification: Notification | null = await models.createObject<Notification>(
    ModelType.Notification,
    getNotificationObjectFromInput(notificationInput),
    serviceRequest,
  )

  if (!notification) {
    logger.error('services.messaging.mergeWithTemplateAndSave: failed save message.',
      { notificationInput }, { remote: true })
    throw new Error(ErrorCode.systemError)
  }

  appHelpers.publishAppEvent<Models.ObjectChangedAppEventData>(
    AppEventType.objectChanged,
    {
      objectId: notification.id,
      modelType: ModelType.Notification,
      messageType: Models.ObjectChangedEventType.created,
      serviceRequest,
    },
  )

  if (sendOut) {
    await sendNotification(
      notification.id,
      false,
      serviceRequest,
    )
  }

  return notification
}

export default createNotification
