import { ErrorCode } from '../../app/types/ErrorCode.js'
import { NotificationTemplateName, NotificationType } from './types/enums.js'
import { User } from '../accounts/types/classes/User.js'
import logger from '../logger/index.js'

const getTemplateNameByNotificationType = (
  notificationType: NotificationType,
  sender?: User | null,
  recipient?: User | null,
): NotificationTemplateName => {
  // logger.trace('services.messaging.getTemplateNameByNotificationType called.',
  //   { notificationType })

  if (!notificationType) {
    logger.error('services.messaging.getTemplateNameByNotificationType: no notificationType given.',
      { notificationType }, { remote: true })
    throw new Error(ErrorCode.systemError)
  }

  const recipientIsMentor = recipient?.offersHelp

  if (notificationType == NotificationType.accountDeletedConfirmation) {
    return NotificationTemplateName.accountDeletedConfirmation
  }

  if (notificationType == NotificationType.channelInvitationAccepted) {
    return recipientIsMentor
      ? NotificationTemplateName.channelInvitationAcceptedForMentor
      : NotificationTemplateName.channelInvitationAcceptedForMentee
  }

  if (notificationType == NotificationType.channelInvitationDeclined) {
    return recipientIsMentor
      ? NotificationTemplateName.channelInvitationDeclinedForMentor
      : NotificationTemplateName.channelInvitationDeclinedForMentee
  }

  if (notificationType == NotificationType.channelInvitationReceived) {
    return recipientIsMentor
      ? NotificationTemplateName.channelInvitationReceivedForMentor
      : NotificationTemplateName.channelInvitationReceivedForMentee
  }

  if (notificationType == NotificationType.channelMessageReceived) {
    return recipientIsMentor
      ? NotificationTemplateName.channelMessageReceivedForMentor
      : NotificationTemplateName.channelMessageReceivedForMentee
  }

  if (notificationType == NotificationType.completeProfile) {
    return recipientIsMentor
      ? NotificationTemplateName.completeProfileForMentor
      : NotificationTemplateName.completeProfileForMentee
  }

  if (notificationType == NotificationType.completeSignUp) {
    return recipientIsMentor
      ? NotificationTemplateName.completeSignUpForMentor
      : NotificationTemplateName.completeSignUpForMentee
  }

  if (notificationType == NotificationType.matchesRecommendations) {
    return recipientIsMentor
      ? NotificationTemplateName.matchesRecommendationsForMentor
      : NotificationTemplateName.matchesRecommendationsForMentee
  }

  if (notificationType == NotificationType.newPrivacyRules) {
    return NotificationTemplateName.newPrivacyRules
  }

  if (notificationType == NotificationType.resetPasswordConfirmation) {
    return NotificationTemplateName.resetPasswordConfirmation
  }

  if (notificationType == NotificationType.resetPasswordConfirmToken) {
    return NotificationTemplateName.resetPasswordConfirmToken
  }

  if (notificationType == NotificationType.sendFirstInvitation) {
    return recipientIsMentor
      ? NotificationTemplateName.sendFirstInvitationForMentor
      : NotificationTemplateName.sendFirstInvitationForMentee
  }

  if (notificationType == NotificationType.welcome) {
    return recipientIsMentor
      ? NotificationTemplateName.welcomeForMentor
      : NotificationTemplateName.welcomeForMentee
  }

  logger.error('services.messaging.getTemplateNameByNotificationType: unknown notificationType given.',
    { notificationType }, { remote: true })
  throw new Error(ErrorCode.systemError)
}

export default getTemplateNameByNotificationType
