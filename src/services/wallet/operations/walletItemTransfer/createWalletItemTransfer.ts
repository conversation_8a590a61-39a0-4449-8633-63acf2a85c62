import { AppEventType } from '../../../../app/types/AppEventType.js'
import { <PERSON>rror<PERSON>ode, ServiceRequestResult } from '../../../../app/types/index.js'
import { ModelType } from '../../../../app/types/ModelType.js'
import { OperationType } from '../../../../app/types/OperationType.js'
import { ServiceName } from '../../../../app/types/enums.js'
import { Wallet } from '../../types/models/Wallet.js'
import { WalletItem } from '../../types/models/WalletItem.js'
import { WalletItemTransfer } from '../../types/models/WalletItemTransfer.js'
import { WalletItemTransferInput } from '../../types/models/WalletItemTransferInput.js'
import * as Accounts from '../../../accounts/types/index.js'
import * as Models from '../../../models/types/index.js'
import appHelpers from '../../../../app/helpers.js'
import authorizeOperation from '../../../../app/helpers/authorizeOperation.js'
import getObjectFromInput from '../../../../lib/models/getObjectFromInput.js'
import logger from '../../../logger/index.js'
import sendWalletItemTransferNotification from './sendWalletItemTransferNotification.js'
import updateWalletItem from '../walletItem/updateWalletItem.js'

const createWalletItemTransfer = async (
  input: WalletItemTransferInput,
  serviceRequest: Models.ServiceRequest,
  sender?: Accounts.User | null,
  wallet?: Wallet | null,
  walletItem?: WalletItem | null,
): Promise<WalletItemTransfer | null> => {
  const models = appHelpers.getCoreService<Models.IModelsService>(ServiceName.models)

  if (
    !input.walletItemId ||
    !input.recipientEmail ||
    !input.recipientFullName ||
    !input.messageText
  ) {
    logger.error('services.wallet.createWalletItemTransfer: invalid input.',
      { input }, { remote: true })

    await appHelpers.updateServiceRequest(
      serviceRequest,
      {
        result: ServiceRequestResult.error,
        errorCode: ErrorCode.invalidInput,
      },
      undefined,
      [ModelType.WalletItemTransfer],
      true,
    )
    throw new Error(ErrorCode.invalidInput)
  }

  if (!walletItem) {
    walletItem = await models.findObjectById<WalletItem>(
      ModelType.WalletItem,
      input.walletItemId,
      undefined,
      serviceRequest,
    )

    if (!walletItem) {
      logger.error('services.wallet.createWalletItemTransfer: walletItem not found.',
        { input }, { remote: true })

      await appHelpers.updateServiceRequest(
        serviceRequest,
        {
          result: ServiceRequestResult.error,
          errorCode: ErrorCode.invalidInput,
          message: 'walletItem not found',
        },
        undefined,
        [ModelType.WalletItemTransfer],
        true,
      )
      throw new Error(ErrorCode.notFound)
    }
  }

  if (!wallet) {
    wallet = await models.findObjectById<Wallet>(
      ModelType.Wallet,
      walletItem.walletId,
      undefined,
      serviceRequest,
    )

    if (!wallet) {
      logger.error('services.wallet.createWalletItemTransfer: wallet not found.',
        { input }, { remote: true })

      await appHelpers.updateServiceRequest(
        serviceRequest,
        {
          result: ServiceRequestResult.error,
          errorCode: ErrorCode.invalidInput,
          message: 'wallet not found',
        },
        undefined,
        [ModelType.WalletItemTransfer],
        true,
      )
      throw new Error(ErrorCode.notFound)
    }
  }

  const senderId = wallet.id

  if (!sender) {
    sender = await models.findObjectById<Accounts.User>(
      ModelType.User,
      senderId,
      undefined,
      serviceRequest,
    )

    if (!sender) {
      logger.error('services.wallet.createWalletItemTransfer: sender not found.',
        { input }, { remote: true })

      await appHelpers.updateServiceRequest(
        serviceRequest,
        {
          result: ServiceRequestResult.error,
          errorCode: ErrorCode.invalidInput,
          message: 'sender user not found',
        },
        undefined,
        [ModelType.WalletItemTransfer],
        true,
      )
      throw new Error(ErrorCode.notFound)
    }
  }

  await authorizeOperation({
    modelType: ModelType.WalletItemTransfer,
    changes: input as Partial<WalletItemTransfer>,
    operation: OperationType.create,
    serviceRequest,
    otherObjects: { sender, wallet, walletItem },
  })

  const changes = getObjectFromInput<WalletItemTransfer, WalletItemTransferInput>(
    ModelType.WalletItemTransfer,
    input,
  )

  const walletItemTransfer = await models.createObject<WalletItemTransfer>(
    ModelType.WalletItemTransfer,
    changes,
    serviceRequest,
  )

  await updateWalletItem(
    { id: walletItem.id, transferredAt: new Date() },
    { returnReloadedObject: false },
    serviceRequest,
    walletItem,
  )

  serviceRequest = await appHelpers.updateServiceRequest(
    serviceRequest,
    {
      result: ServiceRequestResult.ok,
    },
    // This lets the requesting scope know the model IDs of all involved objects.
    // Then they can directly request any of the objects, without having to resolve
    // their ID through the relationships.
    [walletItemTransfer.id, senderId, wallet.id, walletItem.id],
    [ModelType.WalletItemTransfer, ModelType.User, ModelType.Wallet, ModelType.WalletItem],
    true,
  )

  await sendWalletItemTransferNotification(
    walletItemTransfer.id,
    serviceRequest,
    sender,
    wallet,
    walletItem,
  )

  appHelpers.publishAppEvent<Models.ObjectChangedAppEventData>(
    AppEventType.objectChanged,
    {
      objectId: walletItem.id,
      modelType: ModelType.WalletItem,
      messageType: Models.ObjectChangedEventType.updated,
      ownerUserId: senderId,
      serviceRequest,
    },
  )

  appHelpers.publishAppEvent<Models.ObjectChangedAppEventData>(
    AppEventType.objectChanged,
    {
      objectId: walletItemTransfer.id,
      modelType: ModelType.WalletItemTransfer,
      messageType: Models.ObjectChangedEventType.created,
      ownerUserId: senderId,
      serviceRequest,
    },
  )

  logger.trace('services.walletItemTransfer.createWalletItemTransferNotification: done.',
    { walletItemTransfer, recipientEmail: walletItemTransfer.recipientEmail })

  return walletItemTransfer
}

export default createWalletItemTransfer
